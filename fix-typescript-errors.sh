#!/bin/bash

echo "🔧 Fixing TypeScript errors in crefy-connect-frontend..."

# Clean up existing installations
echo "📦 Cleaning up existing installations..."
rm -rf node_modules
rm -f package-lock.json
rm -f pnpm-lock.yaml

# Clear TypeScript cache
echo "🧹 Clearing TypeScript cache..."
rm -rf .next
rm -f tsconfig.tsbuildinfo

# Install dependencies with specific versions that work well together
echo "📥 Installing compatible dependencies..."

# Use npm to ensure consistent dependency resolution
npm install

# Update wagmi and viem to compatible versions
echo "🔄 Updating blockchain dependencies to compatible versions..."
npm install wagmi@^2.15.6 @wagmi/core@^2.17.3 viem@^2.31.3

# Install missing type definitions
echo "📝 Installing additional type definitions..."
npm install --save-dev @types/node@^20 @types/react@^18 @types/react-dom@^18

# Clear TypeScript cache again after dependency changes
echo "🧹 Clearing TypeScript cache after updates..."
rm -rf .next
rm -f tsconfig.tsbuildinfo

echo "✅ TypeScript error fixes applied!"
echo "🚀 Try running 'npm run dev' or 'npx tsc --noEmit' to check for remaining errors."