# Wallet Integration Migration

## Changes Made

### 1. Removed Dependencies
- `@rainbow-me/rainbowkit`
- `wagmi`
- `@wagmi/core`
- `viem`

### 2. Added Dependencies
- `@coinbase/wallet-sdk` (to be installed)

### 3. New Files Created
- `lib/coinbase-wallet-provider.tsx` - New Coinbase Wallet provider
- `components/shared/coinbase-wallet-connect.tsx` - New wallet connection component

### 4. Updated Files
- `app/layout.tsx` - Replaced WagmiProviderWrapper with CoinbaseWalletProvider

### 5. Installation Instructions

To complete the migration, run:

```bash
npm uninstall @rainbow-me/rainbowkit wagmi @wagmi/core viem
npm install @coinbase/wallet-sdk
```

### 6. Usage

The new Coinbase Wallet integration provides:
- Direct connection to Coinbase Wallet
- Chain switching (Mainnet/Sepolia)
- Error handling
- Connection state management
- Fallback to Coinbase Wallet SDK if extension not available

### 7. Components to Update

Replace any usage of the old `WalletConnect` component with the new `CoinbaseWalletConnect` component:

```tsx
import { CoinbaseWalletConnect } from '@/components/shared/coinbase-wallet-connect';

// Usage
<CoinbaseWalletConnect 
  onConnect={(address) => console.log('Connected:', address)}
  onDisconnect={() => console.log('Disconnected')}
/>
```

### 8. Provider Usage

The new provider can be used throughout the app:

```tsx
import { useCoinbaseWallet } from '@/lib/coinbase-wallet-provider';

function MyComponent() {
  const { isConnected, address, connect, disconnect } = useCoinbaseWallet();
  
  // Use wallet state and functions
}
```