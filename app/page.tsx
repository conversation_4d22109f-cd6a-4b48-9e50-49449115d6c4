'use client';
import React, { useState, useEffect, useRef } from 'react';

// Import the Ballpit component from your file
import Ballpit from '@/components/ui/balls'; // Adjust path if needed

// Temporary workaround for development
export const dynamic = 'force-dynamic';

// Floating gradient blobs component
const FloatingBlobs = () => {
  const [blobs, setBlobs] = useState([]);

  useEffect(() => {
    const newBlobs = Array.from({ length: 15 }, (_, i) => ({
      id: i,
      x: Math.random() * 100,
      y: Math.random() * 100,
      size: Math.random() * 150 + 80,
      speed: Math.random() * 25 + 15,
      hue: [200, 220, 260, 280, 300, 320][Math.floor(Math.random() * 6)],
      opacity: Math.random() * 0.08 + 0.03,
    }));
    setBlobs(newBlobs);
  }, []);

  return (
    <div className="absolute inset-0 overflow-hidden pointer-events-none">
      {blobs.map((blob) => (
        <div
          key={blob.id}
          className="absolute rounded-full blur-3xl"
          style={{
            left: `${blob.x}%`,
            top: `${blob.y}%`,
            width: `${blob.size}px`,
            height: `${blob.size}px`,
            background: `radial-gradient(circle, hsla(${blob.hue}, 50%, 65%, ${blob.opacity}) 0%, transparent 70%)`,
            animation: `floatBlob ${blob.speed}s ease-in-out infinite`,
            animationDelay: `${blob.id * 0.6}s`,
          }}
        />
      ))}
    </div>
  );
};

// Original Orb component for rings
const OrbitalOrb = ({ size, hue, speed, delay, isHovered, isRedirecting }) => {
  return (
    <div 
      className={`absolute rounded-full transition-all duration-1000 ${
        isRedirecting ? 'animate-spin' : isHovered ? 'scale-105' : ''
      }`}
      style={{
        width: `${size}px`,
        height: `${size}px`,
        background: `radial-gradient(circle, hsla(${hue}, 60%, 70%, 0.15) 0%, hsla(${hue + 30}, 70%, 75%, 0.1) 50%, transparent 70%)`,
        filter: `blur(20px) brightness(${isHovered ? 1.3 : 1.1})`,
        animation: isRedirecting 
          ? `spin ${speed}s linear infinite ${delay > 0 ? 'reverse' : ''}` 
          : `orbitPulse ${speed}s ease-in-out infinite`,
        animationDelay: `${delay}s`,
        transform: 'translate(-50%, -50%)',
        left: '50%',
        top: '50%',
      }}
    />
  );
};

// Orbital rings system using orb effects
const OrbitalRings = ({ isHovered, isRedirecting }) => {
  return (
    <div className="absolute inset-0 flex items-center justify-center pointer-events-none">
      {/* Ring 1 - Outermost */}
      <OrbitalOrb 
        size={280} 
        hue={240} 
        speed={8} 
        delay={0} 
        isHovered={isHovered} 
        isRedirecting={isRedirecting} 
      />
      
      {/* Ring 2 - Middle */}
      <OrbitalOrb 
        size={220} 
        hue={260} 
        speed={6} 
        delay={1} 
        isHovered={isHovered} 
        isRedirecting={isRedirecting} 
      />
      
      {/* Ring 3 - Innermost */}
      <OrbitalOrb 
        size={160} 
        hue={280} 
        speed={4} 
        delay={2} 
        isHovered={isHovered} 
        isRedirecting={isRedirecting} 
      />
    </div>
  );
};

export default function RootPage() {
  const [isRedirecting, setIsRedirecting] = useState(false);
  const [mousePosition, setMousePosition] = useState({ x: 50, y: 50 });
  const [isHovered, setIsHovered] = useState(false);

  const handleClick = () => {
    setIsRedirecting(true);
    setTimeout(() => {
      window.location.href = '/landing';
    }, 800);
  };

  useEffect(() => {
    if (isRedirecting) {
      document.body.style.cursor = 'wait';
    }
    return () => {
      document.body.style.cursor = 'default';
    };
  }, [isRedirecting]);

  useEffect(() => {
    const handleMouseMove = (e) => {
      setMousePosition({
        x: (e.clientX / window.innerWidth) * 100,
        y: (e.clientY / window.innerHeight) * 100,
      });
    };

    window.addEventListener('mousemove', handleMouseMove);
    return () => window.removeEventListener('mousemove', handleMouseMove);
  }, []);

  return (
    <div className="relative flex min-h-screen items-center justify-center overflow-hidden bg-white">
      {/* Ball Pit Background */}
      <div className="absolute inset-0 z-0">
        <Ballpit
          className="w-full h-full"
          followCursor={true}
          count={50}
          colors={["#4B0082", "#B497D6"]}
          ambientColor={0xffffff}
          ambientIntensity={1}
          lightIntensity={200}
          materialParams={{
            metalness: 0.5,
            roughness: 0.5,
            clearcoat: 1,
            clearcoatRoughness: 0.15,
          }}
          minSize={0.5}
          maxSize={1}
          size0={1}
          gravity={0.5}
          friction={0.9975}
          wallBounce={0.95}
          maxVelocity={0.15}
          maxX={5}
          maxY={5}
          maxZ={2}
        />
      </div>

      {/* Subtle mouse-following gradient */}
      <div 
        className="absolute inset-0 transition-all duration-1000 ease-out opacity-20"
        style={{
          background: `radial-gradient(circle at ${mousePosition.x}% ${mousePosition.y}%, 
            rgba(139, 92, 246, 0.08) 0%, 
            transparent 60%)`,
        }}
      />

      {/* Floating gradient blobs */}
      <FloatingBlobs />

      {/* Main content container */}
      <div className="relative z-10 flex flex-col items-center space-y-12">
        {/* Brand section */}
        <div className="text-center space-y-6">
          <h1 className="text-5xl font-light tracking-wide text-gray-900">
            <span className="font-normal bg-gradient-to-r from-[#4B0082] via-[#4B0082] to-[#B497D6] bg-clip-text text-transparent">
              Crefy
            </span>{' '}
            Connect
          </h1>
          <p className="text-lg text-gray-600 font-light max-w-md leading-relaxed">
            Web3 Identity Infrastructure for Developers
          </p>
          <div className="w-24 h-px bg-gradient-to-r from-transparent via-purple-400 to-transparent mx-auto opacity-40" />
        </div>

        {/* Button with orbital rings */}
        <div className="relative">
          {/* Orbital rings using orb effects */}
          <OrbitalRings isHovered={isHovered} isRedirecting={isRedirecting} />
          
          {/* Main button */}
          <button
            onClick={handleClick}
            disabled={isRedirecting}
            onMouseEnter={() => setIsHovered(true)}
            onMouseLeave={() => setIsHovered(false)}
            className={`relative flex items-center justify-center px-8 py-4 rounded-full transition-all duration-500 transform
              ${isRedirecting 
                ? 'scale-105' 
                : 'hover:scale-110 active:scale-95'
              }
              focus:outline-none group overflow-hidden min-w-32`}
            style={{
              background: isRedirecting 
                ? 'linear-gradient(135deg, rgba(139, 92, 246, 0.12), rgba(59, 130, 246, 0.12))'
                : 'linear-gradient(135deg, #4B008233, #B497D633)',
              backdropFilter: 'blur(20px)',
              border: '1px solid rgba(139, 92, 246, 0.15)',
              boxShadow: isHovered 
                ? '0 20px 40px rgba(139, 92, 246, 0.12), inset 0 1px 0 rgba(255, 255, 255, 0.8)'
                : '0 10px 30px rgba(139, 92, 246, 0.08), inset 0 1px 0 rgba(255, 255, 255, 0.6)',
            }}
          >
            {/* Inner glow */}
            <div className="absolute inset-0 rounded-full bg-gradient-to-tr from-white/30 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300" />
            
            {/* Button content */}
            <div className="relative z-10 flex items-center justify-center">
              {isRedirecting ? (
                <div className="flex items-center space-x-3">
                  <div className="flex space-x-1">
                    <div className="w-2 h-2 bg-purple-600 rounded-full animate-bounce" />
                    <div className="w-2 h-2 bg-purple-600 rounded-full animate-bounce" style={{ animationDelay: '0.1s' }} />
                    <div className="w-2 h-2 bg-purple-600 rounded-full animate-bounce" style={{ animationDelay: '0.2s' }} />
                  </div>
                  <span className="text-sm font-medium text-gray-700">Connecting...</span>
                </div>
              ) : (
                <span className="text-lg font-medium bg-gradient-to-r from-[#4B0082] via-[#4B0082] to-[#B497D6] bg-clip-text text-transparent">
                  Connect
                </span>
              )}
            </div>
          </button>
        </div>

        {/* Status text */}
        <p className="text-sm text-gray-500 font-light">
          {isRedirecting ? 'Initializing connection...' : 'Enter the decentralized future'}
        </p>
      </div>

      <style dangerouslySetInnerHTML={{__html: `
        @keyframes floatBlob {
          0%, 100% { 
            transform: translateY(0px) translateX(0px) scale(1);
          }
          25% { 
            transform: translateY(-25px) translateX(15px) scale(1.05);
          }
          50% { 
            transform: translateY(-15px) translateX(-20px) scale(0.95);
          }
          75% { 
            transform: translateY(20px) translateX(8px) scale(1.02);
          }
        }
        
        @keyframes orbitPulse {
          0%, 100% { 
            opacity: 0.15;
            transform: translate(-50%, -50%) scale(1) rotate(0deg);
          }
          50% { 
            opacity: 0.25;
            transform: translate(-50%, -50%) scale(1.05) rotate(180deg);
          }
        }
        
        @keyframes spin {
          from { transform: translate(-50%, -50%) rotate(0deg); }
          to { transform: translate(-50%, -50%) rotate(360deg); }
        }
      `}} />
    </div>
  );
}