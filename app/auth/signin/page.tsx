"use client";

import { useState } from "react";
import Link from "next/link";
import { useRouter } from "next/navigation";
import { useAuth } from "@/lib/auth-context";
import { apiService, User } from "@/lib/api";

export default function SignInPage() {
  const [email, setEmail] = useState("");
  const [password, setPassword] = useState("");
  const [status, setStatus] = useState("");
  const [isLoading, setIsLoading] = useState(false);
  const router = useRouter();
  const { login } = useAuth();

  const handleSubmit = async (e: React.FormEvent<HTMLFormElement>) => {
    e.preventDefault();
    setIsLoading(true);
    setStatus("Signing in...");

    try {
      // Use the backend API for login - backend will determine user role
      const response = await apiService.login({
        email,
        password,
      });

      if (response.success && response.data) {
        setStatus("Welcome back! Redirecting...");
        
        // Backend should return user role in the response
        // For now, we'll default to "developer" since the API User type doesn't include role
        const userRole = (response.data.user as User & { role?: string }).role || "developer";
        login(response.data.user, response.data.token, userRole);
        
        // Redirect based on user role
        setTimeout(() => {
          if (userRole === "admin") {
            router.push("/dashboard/admin");
          } else {
            router.push("/dashboard/dashboard");
          }
        }, 1000);
      } else {
        setStatus(response.error || "Invalid email or password");
        setIsLoading(false);
      }
    } catch {
      setStatus("Error signing in. Please try again.");
      setIsLoading(false);
    }
  };

  return (
    <main className="min-h-screen flex items-center justify-center bg-white relative overflow-hidden px-4">
      {/* Animated Background Elements */}
      <div className="absolute w-64 h-64 bg-[#7B1FA2]/10 rounded-full blur-3xl animate-pulse"></div>
      <div className="absolute w-80 h-80 bg-[#4A148C]/5 rounded-full blur-3xl animate-float" style={{ top: "20%", left: "10%" }}></div>
      <div className="absolute w-48 h-48 bg-[#7B1FA2]/5 rounded-full blur-3xl animate-bounce-slow" style={{ bottom: "20%", right: "10%" }}></div>

      {/* Login Card */}
      <div className="relative z-10 w-full max-w-md p-8 bg-white/80 backdrop-blur-sm border border-gray-200 rounded-2xl shadow-xl space-y-6 transition-all duration-300 hover:shadow-2xl">
        <div className="text-center">
          <h1 className="text-2xl font-bold bg-gradient-to-r from-[#4A148C] to-[#7B1FA2] bg-clip-text text-transparent">
            Sign In to Crefy Connect
          </h1>
          <p className="text-gray-600 mt-2">Welcome back to your account</p>
        </div>

        {/* Form */}
        <form onSubmit={handleSubmit} className="space-y-5">
          <div className="space-y-2">
            <label htmlFor="email" className="block text-sm font-medium text-gray-700">
              Email
            </label>
            <input
              id="email"
              type="email"
              value={email}
              onChange={(e) => setEmail(e.target.value)}
              required
              className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-[#7B1FA2] transition-shadow duration-200"
              placeholder="<EMAIL>"
            />
          </div>

          <div className="space-y-2">
            <label htmlFor="password" className="block text-sm font-medium text-gray-700">
              Password
            </label>
            <input
              id="password"
              type="password"
              value={password}
              onChange={(e) => setPassword(e.target.value)}
              required
              className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-[#7B1FA2] transition-shadow duration-200"
              placeholder="••••••••"
            />
          </div>

          {/* Status Message */}
          {status && (
            <p
              className={`text-sm ${
                status.includes("Invalid") || status.includes("Error") || status.includes("failed")
                  ? "text-red-500"
                  : "text-gray-600"
              }`}
            >
              {status}
            </p>
          )}

          <button
            type="submit"
            disabled={isLoading}
            className="w-full py-3 bg-[#4A148C] text-white rounded-lg hover:bg-opacity-90 transition-all duration-300 shadow-md hover:shadow-lg font-medium disabled:opacity-50"
          >
            {isLoading ? "Signing in..." : "Sign In"}
          </button>
        </form>

        <div className="text-center space-y-2">
          <p className="text-sm text-gray-600">
            Don&apos;t have an account?{" "}
            <Link href="/auth/signup" className="text-[#4A148C] hover:underline font-medium">
              Sign up
            </Link>
          </p>
        </div>
      </div>
    </main>
  );
}