// Utility functions for wallet integration with RainbowKit and wagmi
import { FetchEnsNameReturnType } from 'wagmi';
// Remove unused import

/**
 * Get ENS profile data including name, avatar, and other records
 * This is a wrapper around wagmi's ENS functions
 */
export interface ENSProfileData {
  name: FetchEnsNameReturnType;
  avatar: string | null;
  address: string;
}

/**
 * Utility function to format addresses for display
 * @param address Ethereum address to format
 * @param chars Number of characters to display at start and end
 */
export function formatAddress(address: string | undefined, chars = 4): string {
  if (!address) return '';
  return `${address.substring(0, chars)}...${address.substring(address.length - chars)}`;
}

/**
 * Get chain name from chain ID 
 * @param chainId The chain ID
 */
export function getChainName(chainId: number): string {
  switch (chainId) {
    case 1: return 'Ethereum Mainnet';
    case 11155111: return 'Sepolia Testnet';
    case 137: return 'Polygon';
    case 56: return 'BSC';
    case 43114: return 'Avalanche';
    case 42161: return 'Arbitrum';
    case 10: return 'Optimism';
    default: return `Chain ${chainId}`;
  }
}

/**
 * Check if an address is a contract
 * @param address The address to check
 * @param provider The provider to use for the check
 */
export async function isContract(address: string, provider: { getCode(address: string): Promise<string> }): Promise<boolean> {
  try {
    const code = await provider.getCode(address);
    return code !== '0x';
  } catch (error) {
    console.error('Error checking if address is contract:', error);
    return false;
  }
}

export const SUPPORTED_CHAINS = [
  { id: 1, name: 'Ethereum Mainnet' },
  { id: 11155111, name: 'Sepolia Testnet' },
  { id: 137, name: 'Polygon' },
  { id: 10, name: 'Optimism' },
  { id: 42161, name: 'Arbitrum' },
];
