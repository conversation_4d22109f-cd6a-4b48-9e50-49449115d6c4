// In-memory storage for development (replace with database in production)
// This module provides shared storage across all API routes

export interface User {
  id?: string;
  email: string;
  password: string;
  isVerified: boolean;
  createdAt: string;
  updatedAt?: string;
}

export interface OTPData {
  otp: string;
  expiry: Date;
}

// Shared storage instances
export const users = new Map<string, User>();
export const otpStore = new Map<string, OTPData>();

// Helper functions for user management
export function getUserByEmail(email: string): User | undefined {
  return users.get(email);
}

export function setUser(email: string, userData: User): void {
  users.set(email, userData);
}

export function deleteUser(email: string): boolean {
  return users.delete(email);
}

// Helper functions for OTP management
export function getOTP(email: string): OTPData | undefined {
  return otpStore.get(email);
}

export function setOTP(email: string, otpData: OTPData): void {
  otpStore.set(email, otpData);
}

export function deleteOTP(email: string): boolean {
  return otpStore.delete(email);
}

// Cleanup expired OTPs (run periodically)
export function cleanupExpiredOTPs(): void {
  const now = new Date();
  for (const [email, otpData] of otpStore.entries()) {
    if (now > otpData.expiry) {
      otpStore.delete(email);
    }
  }
}

// Initialize cleanup interval (runs every 5 minutes)
if (typeof window === 'undefined') { // Only run on server
  setInterval(cleanupExpiredOTPs, 5 * 60 * 1000);
}