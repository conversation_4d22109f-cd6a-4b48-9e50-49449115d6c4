import axios, { AxiosResponse, AxiosError } from 'axios';

const API_BASE_URL = process.env.NEXT_PUBLIC_API_URL || 'https://api.crefy-connect.crefy.xyz/api/v1';

// Configure axios defaults
axios.defaults.timeout = 30000; // 30 second timeout
axios.defaults.headers.common['Accept'] = 'application/json';
axios.defaults.headers.common['Content-Type'] = 'application/json';

export interface ApiResponse<T = unknown> {
  success: boolean;
  data?: T;
  message?: string;
  error?: string;
}

export interface User {
  id: string;
  email: string;
  isVerified: boolean;
  createdAt?: string;
  updatedAt?: string;
}

// Utility functions for validation
export const validateUserId = (userId: string): boolean => {
  // User ID should be a non-empty string with valid format
  if (!userId || typeof userId !== 'string') {
    return false;
  }
  
  // Check for common valid formats (UUID, MongoDB ObjectId, or custom format)
  const validFormats = [
    /^[0-9a-fA-F]{8}-[0-9a-fA-F]{4}-[0-9a-fA-F]{4}-[0-9a-fA-F]{4}-[0-9a-fA-F]{12}$/, // UUID
    /^[0-9a-fA-F]{24}$/, // MongoDB ObjectId
    /^(dev_|user_)[a-zA-Z0-9_-]+$/, // Custom format with prefix
  ];
  
  return validFormats.some(format => format.test(userId));
};

export const validateEmail = (email: string): boolean => {
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  return emailRegex.test(email);
};

export interface AuthResponse {
  token: string;
  user: User;
}

export interface RegisterRequest {
  email: string;
  password: string;
}

export interface VerifyRequest {
  email: string;
  otp: string;
}

export interface LoginRequest {
  email: string;
  password: string;
}

export interface Application {
  appId: string;
  name: string;
  allowedDomains: string[];
  createdAt: string;
  apiKey?: string;
}

export interface CreateApplicationRequest {
  name: string;
  allowedDomains: string[];
}

export interface VerifyAppRequest {
  appId: string;
  apiKey: string;
}

export interface VerifyAppResponse {
  isValid: boolean;
  appId: string;
  developerId: string;
  allowedDomains: string[];
}

class ApiService {
  private async makeRequest<T>(
    endpoint: string,
    method: 'GET' | 'POST' | 'PUT' | 'DELETE' = 'GET',
    data?: unknown,
    headers?: Record<string, string>
  ): Promise<ApiResponse<T>> {
    try {
      const url = `${API_BASE_URL}${endpoint}`;
      console.log(`Making API request to: ${url}`);
      
      const config = {
        method,
        url,
        data,
        headers: {
          'ngrok-skip-browser-warning': 'true', // Skip ngrok browser warning
          ...headers,
        },
      };

      const response: AxiosResponse<T> = await axios(config);
      
      console.log(`Response status: ${response.status}`);
      console.log(`Response data:`, response.data);

      // Handle successful responses
      return {
        success: true,
        data: response.data,
      };

    } catch (error) {
      console.error('API request failed:', error);
      
      if (axios.isAxiosError(error)) {
        const axiosError = error as AxiosError;
        
        // Handle HTTP error responses
        if (axiosError.response) {
          const status = axiosError.response.status;
          const responseData = axiosError.response.data as Record<string, unknown>;
          
          return {
            success: false,
            error: String(responseData?.error || responseData?.message || `HTTP ${status}: ${axiosError.message}`),
          };
        }
        
        // Handle network errors
        if (axiosError.request) {
          return {
            success: false,
            error: 'Network error. The backend service may be down or unreachable.',
          };
        }
      }
      
      // Handle other errors
      return {
        success: false,
        error: error instanceof Error ? error.message : 'An unexpected error occurred',
      };
    }
  }

  // Auth endpoints
  async register(data: RegisterRequest): Promise<ApiResponse<{ message: string }>> {
    // Validate email format
    if (!validateEmail(data.email)) {
      return {
        success: false,
        error: 'Invalid email format',
      };
    }

    if (!data.password || data.password.length < 6) {
      return {
        success: false,
        error: 'Password must be at least 6 characters long',
      };
    }

    return this.makeRequest('/auth/register', 'POST', data);
  }

  async verify(data: VerifyRequest): Promise<ApiResponse<AuthResponse>> {
    if (!validateEmail(data.email)) {
      return {
        success: false,
        error: 'Invalid email format',
      };
    }

    if (!data.otp || data.otp.length !== 6) {
      return {
        success: false,
        error: 'OTP must be 6 digits',
      };
    }

    return this.makeRequest('/auth/verify', 'POST', data);
  }

  async login(data: LoginRequest): Promise<ApiResponse<AuthResponse>> {
    if (!validateEmail(data.email)) {
      return {
        success: false,
        error: 'Invalid email format',
      };
    }

    if (!data.password) {
      return {
        success: false,
        error: 'Password is required',
      };
    }

    return this.makeRequest('/auth/login', 'POST', data);
  }

  async getProfile(developerId: string, token: string): Promise<ApiResponse<User>> {
    // Validate user ID format
    if (!validateUserId(developerId)) {
      return {
        success: false,
        error: 'Invalid user ID format',
      };
    }

    if (!token || typeof token !== 'string') {
      return {
        success: false,
        error: 'Invalid or missing authentication token',
      };
    }

    return this.makeRequest(`/auth/profile/${developerId}`, 'GET', undefined, {
      Authorization: `Bearer ${token}`,
    });
  }

  // Application management endpoints
  async createApplication(data: CreateApplicationRequest, token: string): Promise<ApiResponse<Application>> {
    console.log('Creating application:', data);
    return this.makeRequest('/apps', 'POST', data, {
      Authorization: `Bearer ${token}`,
    });
  }

  async getApplications(token: string): Promise<ApiResponse<Application[]>> {
    return this.makeRequest('/apps', 'GET', undefined, {
      Authorization: `Bearer ${token}`,
    });
  }

  async verifyApplication(data: VerifyAppRequest): Promise<ApiResponse<VerifyAppResponse>> {
    return this.makeRequest('/apps/verify', 'POST', data);
  }

  async deleteApplication(appId: string, token: string): Promise<ApiResponse<{ message: string }>> {
    return this.makeRequest(`/apps/${appId}`, 'DELETE', undefined, {
      Authorization: `Bearer ${token}`,
    });
  }

  async updateApplication(appId: string, data: Partial<CreateApplicationRequest>, token: string): Promise<ApiResponse<Application>> {
    return this.makeRequest(`/apps/${appId}`, 'PUT', data, {
      Authorization: `Bearer ${token}`,
    });
  }
}

export const apiService = new ApiService();