// Type declarations for viem package to resolve compilation errors

declare module 'viem' {
  export interface Config {
    [key: string]: unknown;
  }
  
  export type Address = `0x${string}`;
  export type Hash = `0x${string}`;
  export type Hex = `0x${string}`;
  
  export interface PublicClient {
      [key: string]: unknown;
  }
  
  export interface WalletClient {
      [key: string]: unknown;
  }
  
  export interface Chain {
    id: number;
    name: string;
    network?: string;
    nativeCurrency: {
      name: string;
      symbol: string;
      decimals: number;
    };
    rpcUrls: {
      default: {
        http: string[];
      };
      public?: {
        http: string[];
      };
    };
    blockExplorers?: {
      default: {
        name: string;
        url: string;
      };
    };
    contracts?: {
      [key: string]: {
        address: Address;
        blockCreated?: number;
      };
    };
    testnet?: boolean;
  }
  
  export interface Transport {
      [key: string]: unknown;
  }
  
  export interface Account {
    address: Address;
    type: string;
  }
  
  export interface Block {
      [key: string]: unknown;
  }
  
  export interface Transaction {
      [key: string]: unknown;
  }
  
  export interface TransactionReceipt {
      [key: string]: unknown;
  }
  
  export interface Log {
      [key: string]: unknown;
  }
  
  export interface ContractFunctionArgs {
      [key: string]: unknown;
  }
  
  export interface ContractFunctionName {
      [key: string]: unknown;
  }
  
  export interface Abi {
      [key: string]: unknown;
  }
  
  export interface TypedData {
      [key: string]: unknown;
  }
  
  export interface Signature {
      [key: string]: unknown;
  }
  
  // Export commonly used functions with flexible signatures
  export function createPublicClient(config: { transport: Transport; chain?: Chain; pollingInterval?: number }): PublicClient;
  export function createWalletClient(config: { transport: Transport; account?: Account; chain?: Chain; pollingInterval?: number }): WalletClient;
  export function http(url?: string, config?: Record<string, unknown>): Transport;
  export function webSocket(url?: string, config?: Record<string, unknown>): Transport;
  export function custom(provider: unknown, config?: Record<string, unknown>): Transport;
  export function fallback(transports: Transport[], config?: Record<string, unknown>): Transport;
  
  // Contract interaction functions
  export function readContract(config: {
    address: Address;
    abi: Abi;
    functionName: string;
    args?: unknown[];
    account?: Account;
    chainId?: number;
  }): Promise<unknown>;
  export function writeContract(config: {
    address: Address;
    abi: Abi;
    functionName: string;
    args?: unknown[];
    account?: Account;
    chainId?: number;
    value?: bigint;
  }): Promise<Hash>;
  export function simulateContract(config: {
    address: Address;
    abi: Abi;
    functionName: string;
    args?: unknown[];
    account?: Account;
    chainId?: number;
    value?: bigint;
  }): Promise<{ result: unknown }>;
  export function deployContract(config: {
    abi: Abi;
    bytecode: Hex;
    args?: unknown[];
    account?: Account;
    chainId?: number;
    value?: bigint;
  }): Promise<Address>;
  export function multicall(config: {
    contracts: Array<{
      address: Address;
      abi: Abi;
      functionName: string;
      args?: unknown[];
    }>;
    blockNumber?: bigint;
    chainId?: number;
  }): Promise<Array<{ result: unknown; status: 'success' | 'failure' }>>;
  
  // Transaction functions
  export function sendTransaction(config: {
    to?: Address;
    from?: Address;
    data?: Hex;
    value?: bigint;
    gas?: bigint;
    gasPrice?: bigint;
    maxFeePerGas?: bigint;
    maxPriorityFeePerGas?: bigint;
    nonce?: number;
    chainId?: number;
  }): Promise<Hash>;
  export function prepareTransactionRequest(config: {
    to?: Address;
    from?: Address;
    data?: Hex;
    value?: bigint;
    gas?: bigint;
    gasPrice?: bigint;
    maxFeePerGas?: bigint;
    maxPriorityFeePerGas?: bigint;
    nonce?: number;
    chainId?: number;
  }): Promise<{ [key: string]: unknown }>;
  export function estimateGas(config: {
    to?: Address;
    from?: Address;
    data?: Hex;
    value?: bigint;
    gasPrice?: bigint;
    maxFeePerGas?: bigint;
    maxPriorityFeePerGas?: bigint;
    chainId?: number;
  }): Promise<bigint>;
  
  // Signing functions
  export function signMessage(config: { message: string | Hex; account: Account }): Promise<Hash>;
  export function signTypedData(config: { domain: TypedData; types: Record<string, unknown>; primaryType: string; message: Record<string, unknown>; account: Account }): Promise<Hash>;
  export function verifyMessage(config: { address: Address; message: string | Hex; signature: Hex }): Promise<boolean>;
  export function verifyTypedData(config: { address: Address; domain: TypedData; types: Record<string, unknown>; primaryType: string; message: Record<string, unknown>; signature: Hex }): Promise<boolean>;
  
  // Event and log functions
  export function getLogs(config: {
    address?: Address | Address[];
    blockHash?: Hash;
    fromBlock?: bigint | number;
    toBlock?: bigint | number;
    event?: { type: string; inputs: unknown[] };
    events?: { type: string; inputs: unknown[] }[];
    strict?: boolean;
  }): Promise<Log[]>;
  export function watchEvent(config: {
    address?: Address | Address[];
    event?: { type: string; inputs: unknown[] };
    events?: { type: string; inputs: unknown[] }[];
    onLogs: (logs: Log[]) => void;
    pollingInterval?: number;
  }): () => void;
  export function watchContractEvent(config: {
    address: Address;
    abi: Abi;
    eventName: string;
    args?: unknown[];
    onLogs: (logs: Log[]) => void;
    pollingInterval?: number;
    strict?: boolean;
  }): () => void;
  
  // Utility functions
  export function getAddress(address: string): Address;
  export function isAddress(address: string): boolean;
  export function parseEther(value: string): bigint;
  export function formatEther(value: bigint): string;
  export function parseUnits(value: string, decimals: number): bigint;
  export function formatUnits(value: bigint, decimals: number): string;
  
  // Re-export everything else as any to avoid type errors
  export * from 'viem';
}