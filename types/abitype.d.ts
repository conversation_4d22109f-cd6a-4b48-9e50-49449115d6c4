// Type declarations for abitype package to resolve compilation errors

declare module 'abitype' {
  // Basic ABI types
  export type Abi = readonly AbiItem[];
  export type AbiItem = AbiFunction | AbiEvent | AbiError | AbiConstructor | AbiFallback | AbiReceive;
  
  export interface AbiFunction {
    type: 'function';
    name: string;
    inputs: readonly AbiParameter[];
    outputs: readonly AbiParameter[];
    stateMutability: 'pure' | 'view' | 'nonpayable' | 'payable';
  }
  
  export interface AbiEvent {
    type: 'event';
    name: string;
    inputs: readonly AbiEventParameter[];
    anonymous?: boolean;
  }
  
  export interface AbiError {
    type: 'error';
    name: string;
    inputs: readonly AbiParameter[];
  }
  
  export interface AbiConstructor {
    type: 'constructor';
    inputs: readonly AbiParameter[];
    stateMutability?: 'nonpayable' | 'payable';
  }
  
  export interface AbiFallback {
    type: 'fallback';
    stateMutability: 'nonpayable' | 'payable';
  }
  
  export interface AbiReceive {
    type: 'receive';
    stateMutability: 'payable';
  }
  
  export interface AbiParameter {
    name: string;
    type: string;
    internalType?: string;
    components?: readonly AbiParameter[];
  }
  
  export interface AbiEventParameter extends AbiParameter {
    indexed?: boolean;
  }
  
  // Type utilities
  export type ExtractAbiFunction<TAbi extends Abi, TFunctionName extends string> = Extract<TAbi[number], { type: 'function'; name: TFunctionName }>;
  export type ExtractAbiFunctionNames<TAbi extends Abi> = Extract<TAbi[number], { type: 'function' }>['name'];
  export type ExtractAbiEvent<TAbi extends Abi, TEventName extends string> = Extract<TAbi[number], { type: 'event'; name: TEventName }>;
  export type ExtractAbiEventNames<TAbi extends Abi> = Extract<TAbi[number], { type: 'event' }>['name'];
  export type ExtractAbiError<TAbi extends Abi, TErrorName extends string> = Extract<TAbi[number], { type: 'error'; name: TErrorName }>;
  export type ExtractAbiErrorNames<TAbi extends Abi> = Extract<TAbi[number], { type: 'error' }>['name'];
  
  
  // Human readable ABI types
  export type FormatAbi<TAbi extends Abi> = TAbi; // Using TAbi in the return type
  export type FormatAbiItem<TAbiItem extends AbiItem> = TAbiItem; // Using TAbiItem in the return type
  export type FormatAbiParameter<TAbiParameter extends AbiParameter> = TAbiParameter;
  export type FormatAbiParameters<TAbiParameters extends readonly AbiParameter[]> = TAbiParameters;
  
  export type ParseAbi<TSignatures extends readonly string[]> = { [K in keyof TSignatures]: unknown };
  export type ParseAbiItem<TSignature extends string> = { signature: TSignature; type: unknown };

  
  // Utility functions
  export function formatAbi(abi: Abi): string[];
  export function formatAbiItem(abiItem: AbiItem): string;
  export function formatAbiParameter(abiParameter: AbiParameter): string;
  export function formatAbiParameters(abiParameters: readonly AbiParameter[]): string;
  
  export function parseAbi(signatures: readonly string[]): Abi;
  export function parseAbiItem(signature: string): AbiItem;
  export function parseAbiParameter(param: string): AbiParameter;
  export function parseAbiParameters(params: string): readonly AbiParameter[];
  
  // Re-export everything else as unknown to avoid type errors
  export interface AdditionalExports {
    [key: string]: unknown;
  }
}