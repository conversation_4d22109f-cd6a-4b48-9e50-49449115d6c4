#!/bin/bash

echo "Fixing React hooks error by updating dependencies..."

# Remove node_modules and package-lock.json to ensure clean install
echo "Cleaning up existing installations..."
rm -rf node_modules package-lock.json .next

# Clear npm cache
echo "Clearing npm cache..."
npm cache clean --force

# Install dependencies
echo "Installing updated dependencies..."
npm install

echo ""
echo "✅ Dependencies updated successfully!"
echo ""
echo "Changes made:"
echo "- Downgraded React from v19 to v18.3.1 (stable version)"
echo "- Downgraded Next.js from v15 to v14.2.5 (compatible with React 18)"
echo "- Updated wagmi and related packages to compatible versions"
echo "- Fixed RainbowKit package name and version"
echo ""
echo "🚀 You can now run 'npm run dev' to start the development server."
echo ""
echo "If you still encounter issues, try:"
echo "1. Restart your development server"
echo "2. Clear browser cache"
echo "3. Check that no other React versions are installed globally"