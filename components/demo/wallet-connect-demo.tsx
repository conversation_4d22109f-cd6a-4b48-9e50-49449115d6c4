'use client';

import { useState } from 'react';
import { RainbowWalletConnect } from '@/components/shared/rainbow-wallet-connect';
import { CoinbaseWalletConnect } from '@/components/shared/coinbase-wallet-connect';
import { MultiWalletConnect } from '@/components/shared/multi-wallet-connect';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';

export default function WalletConnectDemo() {
  const [connectedAddress, setConnectedAddress] = useState<string | null>(null);
  
  const handleConnect = (address: string) => {
    setConnectedAddress(address);
  };
  
  const handleDisconnect = () => {
    setConnectedAddress(null);
  };
  
  return (
    <div className="container mx-auto py-8">
      <h1 className="text-3xl font-bold mb-8 text-center">Wallet Connection Options</h1>
      
      <Tabs defaultValue="multi" className="max-w-2xl mx-auto">
        <TabsList className="grid grid-cols-3 mb-8">
          <TabsTrigger value="multi">Custom UI</TabsTrigger>
          <TabsTrigger value="rainbow">RainbowKit</TabsTrigger>
          <TabsTrigger value="coinbase">Coinbase</TabsTrigger>
        </TabsList>
        
        <TabsContent value="multi">
          <Card>
            <CardHeader>
              <CardTitle>Multi-Wallet Connect</CardTitle>
              <CardDescription>
                Custom UI supporting multiple wallet options through wagmi
              </CardDescription>
            </CardHeader>
            <CardContent className="flex justify-center">
              <MultiWalletConnect 
                onConnect={handleConnect} 
                onDisconnect={handleDisconnect}
              />
            </CardContent>
          </Card>
        </TabsContent>
        
        <TabsContent value="rainbow">
          <Card>
            <CardHeader>
              <CardTitle>RainbowKit Connect</CardTitle>
              <CardDescription>
                Using RainbowKit for an elegant connection experience
              </CardDescription>
            </CardHeader>
            <CardContent className="flex justify-center">
              <RainbowWalletConnect 
                onConnect={handleConnect} 
                onDisconnect={handleDisconnect}
              />
            </CardContent>
          </Card>
        </TabsContent>
        
        <TabsContent value="coinbase">
          <Card>
            <CardHeader>
              <CardTitle>Coinbase Wallet Connect</CardTitle>
              <CardDescription>
                Dedicated Coinbase Wallet connection experience
              </CardDescription>
            </CardHeader>
            <CardContent className="flex justify-center">
              <CoinbaseWalletConnect 
                onConnect={handleConnect} 
                onDisconnect={handleDisconnect}
              />
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
      
      {connectedAddress && (
        <div className="mt-8 p-4 bg-green-50 border border-green-200 rounded-lg max-w-2xl mx-auto">
          <p className="text-center text-green-800">
            Connected with address: <code className="font-mono">{connectedAddress.slice(0, 6)}...{connectedAddress.slice(-4)}</code>
          </p>
        </div>
      )}
    </div>
  );
}
