'use client';

import { useState, useEffect } from 'react';
import { useConnect, useAccount, useDisconnect, useChainId } from 'wagmi';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Modal } from '@/components/shared/modal';
import { 
  WalletIcon, 
  ExternalLinkIcon, 
  CopyIcon, 
  CheckIcon,
  LogOutIcon,
  ShieldCheckIcon
} from 'lucide-react';

interface WalletConnectProps {
  showDisconnect?: boolean;
  className?: string;
  onConnect?: (address: string) => void;
  onDisconnect?: () => void;
}

export function WalletConnect({ showDisconnect = true, className = '', onConnect, onDisconnect }: WalletConnectProps) {
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [copied, setCopied] = useState(false);
  
  const { connectors, connect, isPending } = useConnect();
  const { address, isConnected } = useAccount();
  const { disconnect } = useDisconnect();
  const chainId = useChainId();

  // Call onConnect callback when wallet connects
  useEffect(() => {
    if (isConnected && address && onConnect) {
      onConnect(address);
    }
  }, [isConnected, address, onConnect]);

  const handleConnect = (connector: Parameters<typeof connect>[0]['connector']) => {
    connect({ connector });
    setIsModalOpen(false);
  };

  const handleDisconnect = () => {
    disconnect();
    if (onDisconnect) {
      onDisconnect();
    }
  };

  const copyAddress = async () => {
    if (address) {
      await navigator.clipboard.writeText(address);
      setCopied(true);
      setTimeout(() => setCopied(false), 2000);
    }
  };

  const shortenAddress = (addr: string) => {
    return `${addr.slice(0, 6)}...${addr.slice(-4)}`;
  };

  const getChainName = (id: number) => {
    switch (id) {
      case 1: return 'Ethereum Mainnet';
      case ********: return 'Sepolia Testnet';
      case 137: return 'Polygon';
      case 56: return 'BSC';
      case 43114: return 'Avalanche';
      case 42161: return 'Arbitrum';
      case 10: return 'Optimism';
      default: return `Chain ${id}`;
    }
  };

  const getWalletIcon = (connectorName: string) => {
    switch (connectorName) {
      case 'MetaMask':
        return '🦊';
      case 'WalletConnect':
        return '🔗';
      case 'Coinbase Wallet':
        return '🔵';
      default:
        return '👛';
    }
  };

  if (isConnected && address) {
    return (
      <Card className={`w-full max-w-md ${className}`}>
        <CardHeader className="pb-3">
          <CardTitle className="flex items-center gap-2 text-lg">
            <ShieldCheckIcon className="h-5 w-5 text-green-600" />
            Wallet Connected
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="space-y-3">
            <div className="flex items-center justify-between p-3 bg-green-50 border border-green-200 rounded-lg">
              <div className="flex items-center gap-2">
                <div className="w-2 h-2 bg-green-500 rounded-full animate-pulse"></div>
                <span className="text-sm font-medium text-green-800">Connected</span>
              </div>
              <Badge variant="outline" className="bg-green-100 text-green-800 border-green-300">
                {getChainName(chainId || 1)}
              </Badge>
            </div>
            
            <div className="flex items-center justify-between p-3 bg-gray-50 border border-gray-200 rounded-lg">
              <div className="flex items-center gap-2">
                <WalletIcon className="h-4 w-4 text-gray-600" />
                <code className="text-sm font-mono text-gray-800">
                  {shortenAddress(address)}
                </code>
              </div>
              <Button
                variant="ghost"
                size="sm"
                onClick={copyAddress}
                className="h-8 w-8 p-0 hover:bg-gray-200"
              >
                {copied ? (
                  <CheckIcon className="h-4 w-4 text-green-600" />
                ) : (
                  <CopyIcon className="h-4 w-4 text-gray-600" />
                )}
              </Button>
            </div>
          </div>
          
          {showDisconnect && (
            <Button
              variant="outline"
              onClick={handleDisconnect}
              className="w-full flex items-center gap-2 hover:bg-red-50 hover:border-red-200 hover:text-red-700"
            >
              <LogOutIcon className="h-4 w-4" />
              Disconnect Wallet
            </Button>
          )}
        </CardContent>
      </Card>
    );
  }

  return (
    <>
      <Button
        onClick={() => setIsModalOpen(true)}
        className={`flex items-center gap-2 ${className}`}
        disabled={isPending}
      >
        <WalletIcon className="h-4 w-4" />
        {isPending ? 'Connecting...' : 'Connect Wallet'}
      </Button>

      <Modal
        open={isModalOpen}
        onOpenChange={setIsModalOpen}
        title="Connect Your Wallet"
        actionLabel=""
      >
        <div className="space-y-6">
          <div className="text-center">
            <p className="text-sm text-muted-foreground mb-4">
              Choose a wallet to connect to your account. Make sure you have the wallet installed and set up.
            </p>
            <div className="p-3 bg-blue-50 border border-blue-200 rounded-lg">
              <p className="text-xs text-blue-800">
                <ShieldCheckIcon className="inline h-3 w-3 mr-1" />
                Your wallet will remain secure. We never store your private keys.
              </p>
            </div>
          </div>
          
          <div className="space-y-3">
            {connectors.map((connector) => (
              <Button
                key={connector.uid}
                variant="outline"
                className="w-full justify-start h-16 hover:bg-gray-50 border-2 hover:border-blue-200 transition-all"
                onClick={() => handleConnect(connector)}
                disabled={isPending}
              >
                <div className="flex items-center gap-4 w-full">
                  <div className="text-2xl">
                    {getWalletIcon(connector.name)}
                  </div>
                  <div className="text-left flex-1">
                    <div className="font-semibold text-base">{connector.name}</div>
                    <div className="text-xs text-muted-foreground">
                      {connector.name === 'MetaMask' && 'Most popular browser extension wallet'}
                      {connector.name === 'WalletConnect' && 'Scan QR code with your mobile wallet'}
                      {connector.name === 'Coinbase Wallet' && 'Connect with Coinbase Wallet app'}
                      {connector.name === 'Injected' && 'Use your browser wallet'}
                    </div>
                  </div>
                  <div className="text-gray-400">
                    →
                  </div>
                </div>
              </Button>
            ))}
          </div>

          <div className="pt-4 border-t border-gray-200">
            <div className="text-center space-y-2">
              <p className="text-xs text-muted-foreground">Don&apos;t have a wallet?</p>
              <div className="flex justify-center gap-4">
                <Button
                  variant="link"
                  className="p-0 h-auto text-xs"
                  onClick={() => window.open('https://metamask.io/', '_blank')}
                >
                  Get MetaMask
                  <ExternalLinkIcon className="ml-1 h-3 w-3" />
                </Button>
                <Button
                  variant="link"
                  className="p-0 h-auto text-xs"
                  onClick={() => window.open('https://www.coinbase.com/wallet', '_blank')}
                >
                  Get Coinbase Wallet
                  <ExternalLinkIcon className="ml-1 h-3 w-3" />
                </Button>
              </div>
            </div>
          </div>
        </div>
      </Modal>
    </>
  );
}