# Migrating from Coinbase Wallet to RainbowKit

This guide explains how to migrate from the custom Coinbase Wallet provider to RainbowKit with wagmi.

## What Changed

- Replaced custom Coinbase Wallet integration with RainbowKit + wagmi
- Improved wallet connection UI with support for multiple wallets
- Better mobile compatibility
- Enhanced chain switching support
- More robust error handling

## Key Files Updated

- `/lib/wagmi-config.ts` - Configuration for wagmi and RainbowKit
- `/lib/wagmi-provider.tsx` - Provider wrapper component
- `/components/shared/rainbow-wallet-connect.tsx` - New wallet connect component
- `/app/layout.tsx` - Updated to use wagmi provider
- `/app/dashboard/dashboard/wallets/page.tsx` - Updated wallet components

## Usage Instructions

### Basic Wallet Connection

```tsx
import { ConnectButton } from '@rainbow-me/rainbowkit';

export function BasicConnect() {
  return (
    <ConnectButton />
  );
}
```

### Custom Wallet Connect Component

```tsx
import { RainbowWalletConnect } from "@/components/shared/rainbow-wallet-connect";

export function MyComponent() {
  return (
    <RainbowWalletConnect 
      onConnect={(address) => console.log(`Connected: ${address}`)}
      onDisconnect={() => console.log('Disconnected')}
      className="w-full"
    />
  );
}
```

### Using wagmi Hooks

```tsx
import { useAccount, useDisconnect, useChainId } from 'wagmi';

export function WalletInfo() {
  const { address, isConnected } = useAccount();
  const { disconnect } = useDisconnect();
  const chainId = useChainId();
  
  if (!isConnected) {
    return <p>Not connected</p>;
  }
  
  return (
    <div>
      <p>Connected: {address}</p>
      <p>Chain ID: {chainId}</p>
      <button onClick={() => disconnect()}>Disconnect</button>
    </div>
  );
}
```

## Required Configuration

Make sure to set your WalletConnect project ID in `/lib/wagmi-config.ts`:

```typescript
export const wagmiConfig = getDefaultConfig({
  appName: 'Crefy Connect',
  projectId: 'YOUR_WALLETCONNECT_PROJECT_ID', // Get this from https://cloud.walletconnect.com/
  chains: [mainnet, sepolia, polygon, optimism, arbitrum],
  // ...
})
```

You can obtain a WalletConnect project ID by signing up at https://cloud.walletconnect.com/

## Additional Resources

- [RainbowKit Documentation](https://www.rainbowkit.com/docs/introduction)
- [wagmi Documentation](https://wagmi.sh/react/getting-started)
- [WalletConnect Documentation](https://docs.walletconnect.com/2.0)
